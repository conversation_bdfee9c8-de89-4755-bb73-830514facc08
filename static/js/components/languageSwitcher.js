/**
 * Language Switcher Component
 * Handles language switching functionality with dropdown interface and localStorage persistence
 */
document.addEventListener("alpine:init", () => {
    Alpine.data("languageSwitcher", () => ({
        open: false,
        currentLanguage: null,
        isLoading: false,

        languages: [
            { code: "bg", name: "Български", flag: "🇧🇬" },
            { code: "en", name: "English", flag: "🇺🇸" },
            { code: "el", name: "Ελληνικ<PERSON>", flag: "🇬🇷" },
            { code: "ru", name: "Русский", flag: "🇷🇺" },
        ],

        init() {
            // Инициализиране на текущия език от localStorage или от HTML документа
            this.initializeCurrentLanguage();

            // Затваряне на dropdown при кликване извън него
            document.addEventListener("click", (e) => {
                if (!this.$root.contains(e.target)) {
                    this.open = false;
                }
            });

            // Слушане за промени в localStorage от други табове
            window.addEventListener("storage", (event) => {
                if (event.key === "preferredLanguage") {
                    this.currentLanguage = event.newValue || "bg";
                }
            });
        },

        /**
         * Инициализира текущия език от localStorage или използва езика от URL/HTML
         */
        initializeCurrentLanguage() {
            // Първо проверяваме localStorage за запазен език
            const savedLanguage = localStorage.getItem("preferredLanguage");

            // Ако има запазен език и е валиден, използваме го
            if (savedLanguage && this.isValidLanguage(savedLanguage)) {
                this.currentLanguage = savedLanguage;

                // Ако текущият URL не съответства на запазения език, пренасочваме
                const urlLanguage = this.getLanguageFromUrl();
                if (urlLanguage !== savedLanguage) {
                    this.navigateToLanguage(savedLanguage, false);
                    return;
                }
            } else {
                // Иначе използваме езика от URL или HTML документа
                this.currentLanguage =
                    this.getLanguageFromUrl() ||
                    document.documentElement.lang ||
                    "bg";

                // Запазваме в localStorage за бъдещи посещения
                this.saveLanguagePreference(this.currentLanguage);
            }
        },

        /**
         * Извлича езиковия код от текущия URL
         */
        getLanguageFromUrl() {
            const pathMatch = window.location.pathname.match(/^\/([a-z]{2})\//);
            return pathMatch ? pathMatch[1] : null;
        },

        /**
         * Проверява дали езиковият код е валиден
         */
        isValidLanguage(languageCode) {
            return this.languages.some((lang) => lang.code === languageCode);
        },

        /**
         * Запазва езиковото предпочитание в localStorage
         */
        saveLanguagePreference(languageCode) {
            try {
                localStorage.setItem("preferredLanguage", languageCode);
            } catch (error) {
                console.warn(
                    "Не може да се запази езиковото предпочитание:",
                    error
                );
            }
        },

        toggle() {
            this.open = !this.open;
        },

        /**
         * Превключва на нов език
         */
        switchLanguage(languageCode) {
            if (languageCode === this.currentLanguage || this.isLoading) {
                this.open = false;
                return;
            }

            // Запазваме новия език в localStorage
            this.saveLanguagePreference(languageCode);

            // Показваме индикатор за зареждане
            this.isLoading = true;

            // Навигираме към новия език
            this.navigateToLanguage(languageCode, true);
        },

        /**
         * Навигира към определен език
         */
        navigateToLanguage(languageCode, showLoading = false) {
            const currentPath = window.location.pathname;
            const pathWithoutLang = currentPath.replace(/^\/[a-z]{2}\//, "/");
            const newPath = `/${languageCode}${
                pathWithoutLang === "/" ? "" : pathWithoutLang
            }`;

            // Добавяме query параметър за да запазим позицията на страницата
            const searchParams = new URLSearchParams(window.location.search);
            const fullUrl =
                newPath +
                (searchParams.toString() ? `?${searchParams.toString()}` : "");

            if (showLoading) {
                // Малко забавяне за по-добра UX
                setTimeout(() => {
                    window.location.href = fullUrl;
                }, 150);
            } else {
                window.location.href = fullUrl;
            }
        },

        getCurrentLanguageName() {
            const lang = this.languages.find(
                (l) => l.code === this.currentLanguage
            );
            return lang ? lang.name : "Български";
        },

        getCurrentLanguageFlag() {
            const lang = this.languages.find(
                (l) => l.code === this.currentLanguage
            );
            return lang ? lang.flag : "🇧🇬";
        },

        /**
         * Проверява дали даден език е текущо избраният
         */
        isCurrentLanguage(languageCode) {
            return this.currentLanguage === languageCode;
        },
    }));
});
