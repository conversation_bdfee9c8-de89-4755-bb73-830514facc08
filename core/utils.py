"""
Утилити за обработка на изображения и други помощни функции
"""

import os
import uuid
from io import BytesIO
from PIL import Image, ImageOps, ExifTags
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


def get_property_image_upload_path(instance, filename):
    """
    Генерира пътя за качване на снимки на имоти с подобрена структура

    Args:
        instance: PropertyImage инстанция
        filename: Оригиналното име на файла

    Returns:
        str: Пътя за качване на файла
    """
    # Извличаме разширението на файла
    ext = filename.split(".")[-1].lower()

    # Генерираме уникално име на файла
    unique_filename = f"{uuid.uuid4().hex}.{ext}"

    # Създаваме структура: property_images/property_id/unique_filename
    return f"property_images/{instance.property_obj.id}/{unique_filename}"


def optimize_image(
    image_file, max_width=1920, max_height=1080, quality=85, format="JPEG"
):
    """
    Оптимизира изображение - преоразмерява, компресира и коригира ориентацията

    Args:
        image_file: Django UploadedFile или PIL Image
        max_width: Максимална ширина в пиксели
        max_height: Максимална височина в пиксели
        quality: Качество на JPEG компресията (1-100)
        format: Формат на изходното изображение

    Returns:
        ContentFile: Оптимизираното изображение като Django ContentFile
    """
    try:
        # Отваряме изображението
        if hasattr(image_file, "read"):
            image = Image.open(image_file)
        else:
            image = image_file

        # Коригираме ориентацията според EXIF данните
        image = fix_image_orientation(image)

        # Конвертираме в RGB ако е необходимо (за JPEG)
        if format.upper() == "JPEG" and image.mode in ("RGBA", "LA", "P"):
            # Създаваме бял фон за прозрачни изображения
            background = Image.new("RGB", image.size, (255, 255, 255))
            if image.mode == "P":
                image = image.convert("RGBA")
            background.paste(
                image, mask=image.split()[-1] if image.mode == "RGBA" else None
            )
            image = background
        elif format.upper() == "PNG" and image.mode not in ("RGBA", "RGB", "L"):
            image = image.convert("RGBA")

        # Преоразмеряваме изображението ако е необходимо
        if image.width > max_width or image.height > max_height:
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

        # Записваме оптимизираното изображение в BytesIO
        output = BytesIO()

        # Настройки за запазване според формата
        save_kwargs = {}
        if format.upper() == "JPEG":
            save_kwargs.update(
                {"quality": quality, "optimize": True, "progressive": True}
            )
        elif format.upper() == "PNG":
            save_kwargs.update({"optimize": True, "compress_level": 6})

        image.save(output, format=format, **save_kwargs)
        output.seek(0)

        # Връщаме като ContentFile
        return ContentFile(output.getvalue())

    except Exception as e:
        logger.error(f"Грешка при оптимизация на изображение: {e}")
        raise


def fix_image_orientation(image):
    """
    Коригира ориентацията на изображението според EXIF данните

    Args:
        image: PIL Image обект

    Returns:
        PIL Image: Коригираното изображение
    """
    try:
        # Проверяваме за EXIF данни
        if hasattr(image, "_getexif"):
            exif = image._getexif()
            if exif is not None:
                # Търсим Orientation тага
                for tag, value in exif.items():
                    if tag in ExifTags.TAGS and ExifTags.TAGS[tag] == "Orientation":
                        # Прилагаме съответната трансформация
                        if value == 2:
                            image = image.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
                        elif value == 3:
                            image = image.rotate(180, expand=True)
                        elif value == 4:
                            image = image.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
                        elif value == 5:
                            image = image.transpose(
                                Image.Transpose.FLIP_LEFT_RIGHT
                            ).rotate(90, expand=True)
                        elif value == 6:
                            image = image.rotate(270, expand=True)
                        elif value == 7:
                            image = image.transpose(
                                Image.Transpose.FLIP_LEFT_RIGHT
                            ).rotate(270, expand=True)
                        elif value == 8:
                            image = image.rotate(90, expand=True)
                        break
    except Exception as e:
        logger.warning(f"Не може да се коригира ориентацията на изображението: {e}")

    return image


def create_image_variants(image_file, base_name):
    """
    Създава различни размери на изображението за responsive дизайн

    Args:
        image_file: Django UploadedFile
        base_name: Базовото име на файла

    Returns:
        dict: Речник с различните варианти на изображението
    """
    variants = {}

    # Дефинираме размерите за различните варианти
    sizes = {
        "thumbnail": (300, 200),  # За карточки на имоти
        "medium": (800, 600),  # За галерии
        "large": (1920, 1080),  # За детайлни изгледи
    }

    try:
        original_image = Image.open(image_file)

        for variant_name, (max_width, max_height) in sizes.items():
            # Създаваме копие на оригиналното изображение
            variant_image = original_image.copy()

            # Оптимизираме за съответния размер
            optimized_file = optimize_image(
                variant_image,
                max_width=max_width,
                max_height=max_height,
                quality=85 if variant_name == "large" else 80,
            )

            # Генерираме име на файла за варианта
            name_parts = base_name.split(".")
            variant_filename = f"{name_parts[0]}_{variant_name}.{name_parts[-1]}"

            variants[variant_name] = {
                "file": optimized_file,
                "filename": variant_filename,
            }

    except Exception as e:
        logger.error(f"Грешка при създаване на варианти на изображението: {e}")

    return variants


def validate_image_file(uploaded_file):
    """
    Валидира качения файл с изображение

    Args:
        uploaded_file: Django UploadedFile или ImageFieldFile

    Returns:
        tuple: (is_valid, error_message)
    """
    # Проверяваме дали файлът съществува
    if not uploaded_file:
        return False, "Няма избран файл."

    # Проверяваме дали това е нов файл (UploadedFile) или съществуващ (ImageFieldFile)
    is_uploaded_file = hasattr(uploaded_file, "content_type")

    # Проверяваме размера на файла (максимум 10MB) - само за нови файлове
    if is_uploaded_file:
        max_size = 10 * 1024 * 1024  # 10MB
        if uploaded_file.size > max_size:
            return False, "Файлът е твърде голям. Максималният размер е 10MB."

        # Проверяваме типа на файла
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
        if uploaded_file.content_type not in allowed_types:
            return False, "Неподдържан формат на файла. Разрешени са: JPEG, PNG, WebP."

    # Проверяваме разширението на файла
    allowed_extensions = [".jpg", ".jpeg", ".png", ".webp"]
    file_extension = os.path.splitext(uploaded_file.name)[1].lower()
    if file_extension not in allowed_extensions:
        return False, "Неподдържано разширение на файла."

    try:
        # Опитваме се да отворим файла като изображение
        image = Image.open(uploaded_file)
        image.verify()  # Проверяваме дали файлът е валидно изображение

        # Проверяваме размерите на изображението
        # За ImageFieldFile трябва да отворим файла отново след verify()
        if hasattr(uploaded_file, "seek"):
            uploaded_file.seek(0)  # Връщаме указателя в началото
        image = Image.open(uploaded_file)
        width, height = image.size

        # Минимални размери - само за нови файлове
        if is_uploaded_file and (width < 300 or height < 200):
            return (
                False,
                "Изображението е твърде малко. Минималните размери са 300x200 пиксела.",
            )

        # Максимални размери - само за нови файлове
        if is_uploaded_file and (width > 5000 or height > 5000):
            return (
                False,
                "Изображението е твърде голямо. Максималните размери са 5000x5000 пиксела.",
            )

    except Exception as e:
        return False, f"Файлът не е валидно изображение: {str(e)}"

    return True, ""


def get_image_info(image_file):
    """
    Извлича информация за изображението

    Args:
        image_file: Django UploadedFile или път до файл

    Returns:
        dict: Информация за изображението
    """
    try:
        image = Image.open(image_file)

        info = {
            "width": image.width,
            "height": image.height,
            "format": image.format,
            "mode": image.mode,
            "size_bytes": getattr(image_file, "size", 0),
        }

        # Добавяме EXIF данни ако са налични
        if hasattr(image, "_getexif") and image._getexif():
            info["has_exif"] = True
        else:
            info["has_exif"] = False

        return info

    except Exception as e:
        logger.error(f"Грешка при извличане на информация за изображението: {e}")
        return {}
