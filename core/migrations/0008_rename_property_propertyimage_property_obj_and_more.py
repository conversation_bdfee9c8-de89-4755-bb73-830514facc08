# Generated by Django 5.2.4 on 2025-07-23 14:56

import core.utils
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_remove_property_slug_bg_remove_property_slug_el_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='propertyimage',
            old_name='property',
            new_name='property_obj',
        ),
        migrations.AlterUniqueTogether(
            name='propertyimage',
            unique_together={('property_obj', 'order')},
        ),
        migrations.AddField(
            model_name='propertyimage',
            name='alt_text',
            field=models.CharField(blank=True, max_length=255, verbose_name='Алтернативен текст'),
        ),
        migrations.AddField(
            model_name='propertyimage',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Дата на създаване'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='propertyimage',
            name='file_size',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Размер на файла'),
        ),
        migrations.AddField(
            model_name='propertyimage',
            name='height',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Височина'),
        ),
        migrations.AddField(
            model_name='propertyimage',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Дата на обновяване'),
        ),
        migrations.AddField(
            model_name='propertyimage',
            name='width',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Ширина'),
        ),
        migrations.AlterField(
            model_name='propertyimage',
            name='image',
            field=models.ImageField(upload_to=core.utils.get_property_image_upload_path, verbose_name='Снимка'),
        ),
    ]
