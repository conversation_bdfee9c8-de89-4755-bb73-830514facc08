{% load static i18n %}

<header class="bg-surface-variant shadow-sm sticky top-0 z-40"
        x-data="mobileMenu"
        @click.outside="close()">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="{% url 'core:home' %}" class="flex items-center">
                    <div class="w-8 h-8 bg-primary rounded-radius mr-3 flex items-center justify-center">
                        <span class="text-on-primary font-bold text-lg">И</span>
                    </div>
                    <span class="font-title text-xl font-bold text-on-surface">Имоти Bulgaria</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex justify-center gap-2 text-sm lg:gap-4 lg:text-base w-full px-12">
                <a href="{% url 'core:home' %}"
                   class="text-on-surface hover:text-primary transition-colors duration-200 px-3 py-2 rounded-radius">
                    {% trans "Начало" %}
                </a>
                <a href="{% url 'core:property-list' %}"
                   class="text-on-surface hover:text-primary transition-colors duration-200 px-3 py-2 rounded-radius">
                    {% trans "Имоти" %}
                </a>
                <a href="{% url 'core:about' %}"
                   class="text-on-surface hover:text-primary transition-colors duration-200 px-3 py-2 rounded-radius">
                    {% trans "За нас" %}
                </a>
                <a href="{% url 'core:contact' %}"
                   class="text-on-surface hover:text-primary transition-colors duration-200 px-3 py-2 rounded-radius">
                    {% trans "Контакти" %}
                </a>
            </nav>

            <!-- Theme Toggle & Mobile Menu Button -->
            <div class="flex items-center space-x-4">
                <!-- Language Switcher (Desktop) -->
                <div x-data="languageSwitcher" class="relative hidden lg:block">
                    <button @click="toggle()"
                            class="btn btn-ghost flex items-center space-x-2"
                            :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
                            :disabled="isLoading"
                            aria-label="{% trans 'Избор на език' %}">
                        <span x-text="getCurrentLanguageFlag()" class="text-lg"></span>
                        <span x-text="getCurrentLanguageName()" class="text-sm font-medium"></span>

                        <!-- Loading Spinner -->
                        <svg x-show="isLoading"
                             class="w-4 h-4 animate-spin text-primary"
                             fill="none"
                             viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>

                        <!-- Dropdown Arrow -->
                        <svg x-show="!isLoading"
                             class="w-4 h-4 transition-transform duration-200"
                             :class="{ 'rotate-180': open }"
                             fill="none"
                             stroke="currentColor"
                             viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div x-show="open && !isLoading"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute right-0 mt-2 w-48 bg-surface border border-outline rounded-radius shadow-lg z-50">
                        <div class="py-1">
                            <template x-for="language in languages" :key="language.code">
                                <button @click="switchLanguage(language.code)"
                                        class="flex items-center w-full px-4 py-2 text-sm text-on-surface hover:bg-surface-variant transition-colors duration-200"
                                        :class="{
                                            'bg-surface-variant text-primary font-medium': isCurrentLanguage(language.code),
                                            'cursor-not-allowed opacity-50': isLoading
                                        }"
                                        :disabled="isLoading">
                                    <span x-text="language.flag" class="mr-3 text-lg"></span>
                                    <span x-text="language.name"></span>
                                    <!-- Checkmark for current language -->
                                    <svg x-show="isCurrentLanguage(language.code)"
                                         class="w-4 h-4 ml-auto text-primary"
                                         fill="none"
                                         stroke="currentColor"
                                         viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </button>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button @click="toggleTheme()"
                        class="btn btn-ghost hidden md:block"
                        aria-label="{% trans 'Превключване на тема' %}">
                    <svg x-show="!darkMode"
                         class="w-5 h-5 text-on-surface"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z">
                        </path>
                    </svg>
                    <svg x-show="darkMode"
                         class="w-5 h-5 text-on-surface"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z">
                        </path>
                    </svg>
                </button>

                <!-- Mobile menu button -->
                <button @click="toggle()"
                        class="btn btn-ghost md:hidden"
                        aria-label="{% trans 'Отваряне на меню' %}">
                    <svg x-show="!open"
                         class="w-6 h-6 text-on-surface"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg x-show="open"
                         class="w-6 h-6 text-on-surface"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Backdrop Overlay for Mobile Menu -->
    <div x-show="open"
         x-transition:enter="transition-opacity ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="close()"
         class="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-30"></div>

    <!-- Mobile Navigation Menu -->
    <nav x-show="open"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="transform -translate-y-full opacity-0"
         x-transition:enter-end="transform translate-y-0 opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="transform translate-y-0 opacity-100"
         x-transition:leave-end="transform -translate-y-full opacity-0"
         class="md:hidden fixed top-0 left-0 right-0 bg-surface border-b border-outline shadow-2xl z-40">

        <!-- Mobile Header -->
        <div class="flex items-center justify-between px-4 py-4 border-b border-outline bg-surface-variant">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-primary rounded-radius mr-3 flex items-center justify-center">
                    <span class="text-on-primary font-bold text-lg">И</span>
                </div>
                <span class="font-title text-lg font-semibold text-on-surface">{% trans "Меню" %}</span>
            </div>
            <button @click="close()" class="btn btn-ghost">
                <svg class="w-6 h-6 text-on-surface"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Navigation Links -->
        <div class="p-4 space-y-2">
            <a href="{% url 'core:home' %}"
               @click="close()"
               class="block px-4 py-4 text-on-surface hover:text-primary hover:bg-surface-variant rounded-radius transition-all duration-200 font-medium group">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-200"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span>{% trans "Начало" %}</span>
                </div>
            </a>
            <a href="{% url 'core:property-list' %}"
               @click="close()"
               class="block px-4 py-4 text-on-surface hover:text-primary hover:bg-surface-variant rounded-radius transition-all duration-200 font-medium group">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-200"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m14 0a2 2 0 01-2 2H7a2 2 0 01-2-2m14 0V9a2 2 0 00-2-2M9 7h6" />
                    </svg>
                    <span>{% trans "Имоти" %}</span>
                </div>
            </a>
            <a href="{% url 'core:about' %}"
               @click="close()"
               class="block px-4 py-4 text-on-surface hover:text-primary hover:bg-surface-variant rounded-radius transition-all duration-200 font-medium group">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-200"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span>{% trans "За нас" %}</span>
                </div>
            </a>
            <a href="{% url 'core:contact' %}"
               @click="close()"
               class="block px-4 py-4 text-on-surface hover:text-primary hover:bg-surface-variant rounded-radius transition-all duration-200 font-medium group">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-200"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span>{% trans "Контакти" %}</span>
                </div>
            </a>
        </div>

        <!-- Language Switcher Section (Mobile) -->
        <div x-data="languageSwitcher" class="px-4 py-4 border-t border-outline">
            <div class="mb-3">
                <div class="flex items-center space-x-3 mb-3">
                    <svg class="w-5 h-5 text-primary"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                        </path>
                    </svg>
                    <span class="text-sm text-on-surface font-medium">{% trans "Език" %}</span>

                    <!-- Loading indicator for mobile -->
                    <svg x-show="isLoading"
                         class="w-4 h-4 animate-spin text-primary ml-auto"
                         fill="none"
                         viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
                <div class="grid grid-cols-2 gap-2" :class="{ 'opacity-50': isLoading }">
                    <template x-for="language in languages" :key="language.code">
                        <button @click="switchLanguage(language.code); close()"
                                class="flex items-center px-3 py-2 text-sm text-on-surface hover:text-primary hover:bg-surface-variant rounded-radius transition-all duration-200 relative"
                                :class="{
                                    'bg-surface-variant text-primary font-medium': isCurrentLanguage(language.code),
                                    'cursor-not-allowed': isLoading
                                }"
                                :disabled="isLoading">
                            <span x-text="language.flag" class="mr-2 text-base"></span>
                            <span x-text="language.name" class="text-sm"></span>
                            <!-- Checkmark for current language -->
                            <svg x-show="isCurrentLanguage(language.code)"
                                 class="w-3 h-3 ml-auto text-primary"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </button>
                    </template>
                </div>
            </div>
        </div>

        <!-- Theme Toggle Section -->
        <div class="px-4 py-4 border-t border-outline bg-surface-variant">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-primary"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3V1m0 22v-2m8-11a4 4 0 014 4v6a2 2 0 01-2 2h-4a2 2 0 01-2-2v-6a4 4 0 014-4zM15 9V7m0 22v-2">
                        </path>
                    </svg>
                    <span class="text-sm text-on-surface font-medium">{% trans "Тема на интерфейса" %}</span>
                </div>
                <button @click="toggleTheme()" class="btn btn-ghost">
                    <svg x-show="!darkMode"
                         class="w-5 h-5 text-on-surface-variant group-hover:text-primary transition-colors duration-200"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                    <svg x-show="darkMode"
                         class="w-5 h-5 text-on-surface-variant group-hover:text-primary transition-colors duration-200"
                         fill="none"
                         stroke="currentColor"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </button>
            </div>
        </div>
    </nav>
</header>
