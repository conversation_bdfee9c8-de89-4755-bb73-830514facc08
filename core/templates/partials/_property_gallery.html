<!-- Image Gallery -->
                <div class="mb-8">
                    {% if property.images.all %}
                        <div x-data="imageGallery()" class="space-y-4">
                            <!-- Main Image -->
                            <div class="relative overflow-hidden rounded-radius">
                                <img x-bind:src="currentImage.url"
                                     x-bind:alt="currentImage.alt"
                                     x-bind:width="currentImage.width"
                                     x-bind:height="currentImage.height"
                                     loading="lazy"
                                     class="w-full h-96 object-cover">

                                <!-- Navigation Arrows -->
                                {% if property.images.count > 1 %}
                                    <button @click="previousImage()"
                                            class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-surface/80 p-2 rounded-full hover:bg-surface">
                                        <svg class="w-6 h-6 text-on-surface"
                                             fill="none"
                                             stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                    </button>
                                    <button @click="nextImage()"
                                            class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-surface/80 p-2 rounded-full hover:bg-surface">
                                        <svg class="w-6 h-6 text-on-surface"
                                             fill="none"
                                             stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                {% endif %}
                            </div>

                            <!-- Thumbnail Strip -->
                            {% if property.images.count > 1 %}
                                <div class="flex space-x-2 overflow-x-auto p-1">
                                    {% for image in property.images.all %}
                                        <button @click="setCurrentImage({{ forloop.counter0 }})"
                                                :class="currentIndex === {{ forloop.counter0 }} ? 'ring-2 ring-primary' : ''"
                                                class="flex-shrink-0 w-20 h-20 rounded-radius overflow-hidden">
                                            <img src="{{ image.thumbnail.url }}"
                                                 alt="{{ image.alt_text|default:'Снимка '|add:forloop.counter }}"
                                                 loading="lazy"
                                                 class="w-full h-full object-cover">
                                        </button>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <script>
                    document.addEventListener('alpine:init', () => {
                        Alpine.data('imageGallery', () => ({
                            currentIndex: 0,
                            images: [
                                {% for image in property.images.all %}
                                {
                                    url: '{{ image.large.url }}',
                                    alt: '{{ image.alt_text|default:"Снимка "|add:forloop.counter|add:" - "|add:property.title }}',
                                    width: {{ image.width|default:1920 }},
                                    height: {{ image.height|default:1080 }}
                                }{% if not forloop.last %},{% endif %}
                                {% endfor %}
                            ],
                            get currentImage() {
                                return this.images[this.currentIndex] || { url: '', alt: '' }
                            },
                            setCurrentImage(index) {
                                this.currentIndex = index
                            },
                            nextImage() {
                                this.currentIndex = (this.currentIndex + 1) % this.images.length
                            },
                            previousImage() {
                                this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1
                            }
                        }))
                    })
                        </script>
                    {% else %}
                        <div class="w-full h-96 bg-surface-variant rounded-radius flex items-center justify-center">
                            <div class="text-center">
                                <svg class="mx-auto h-16 w-16 text-on-surface-variant"
                                     fill="none"
                                     stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <p class="mt-2 text-on-surface-variant">Няма налични снимки</p>
                            </div>
                        </div>
                    {% endif %}
                </div>