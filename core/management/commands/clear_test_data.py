from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from core.models import (
    Property,
    PropertyImage,
    ContactInquiry,
    Testimonial,
    City,
    Location,
    PropertyType,
    Feature,
    TeamMember,
)


class Command(BaseCommand):
    help = _("Изчиства всички данни от базата данни")

    def add_arguments(self, parser):
        parser.add_argument(
            "--confirm",
            action="store_true",
            help=_("Потвърдете, че искате да изчистите всички данни"),
        )
        parser.add_argument(
            "--preserve-cities",
            action="store_true",
            help=_(
                "Запазва данните за градове, локации, типове имоти, особености и екип"
            ),
        )

    def handle(self, *args, **options):
        if not options["confirm"]:
            self.stdout.write(
                self.style.WARNING(
                    _(
                        "ВНИМАНИЕ: Тази команда ще изтрие всички данни от базата!\n"
                        "Това включва:\n"
                        "- Всички имоти и техните снимки\n"
                        "- Всички запитвания от клиенти\n"
                        "- Всички отзиви\n"
                        "- Всички градове и локации\n"
                        "- Всички типове имоти и техните характеристики\n"
                        "- Всички членове на екипа\n\n"
                        "За да потвърдите, изпълнете командата с --confirm флаг:\n"
                        "python manage.py clear_test_data --confirm\n\n"
                        "Ако искате да запазите градове, локации, типове имоти, особености и екип, използвайте:\n"
                        "python manage.py clear_test_data --confirm --preserve-cities"
                    )
                )
            )
            return

        self.stdout.write(_("Започвам изчистването на данните..."))

        # Броене на записите преди изтриване
        properties_count = Property.objects.count()
        images_count = PropertyImage.objects.count()
        inquiries_count = ContactInquiry.objects.count()
        testimonials_count = Testimonial.objects.count()
        cities_count = City.objects.count()
        locations_count = Location.objects.count()
        property_types_count = PropertyType.objects.count()
        features_count = Feature.objects.count()
        team_members_count = TeamMember.objects.count()

        # Изтриване на основните данни
        PropertyImage.objects.all().delete()
        ContactInquiry.objects.all().delete()
        Testimonial.objects.all().delete()
        Property.objects.all().delete()

        # Изтриване на допълнителните данни, освен ако не е зададено да се запазят
        if not options["preserve_cities"]:
            Location.objects.all().delete()
            City.objects.all().delete()
            PropertyType.objects.all().delete()
            Feature.objects.all().delete()
            TeamMember.objects.all().delete()

        # Подготвяне на съобщението с резултатите
        result_message = _(
            f"Успешно изтрити:\n"
            f"- {properties_count} имота\n"
            f"- {images_count} снимки\n"
            f"- {inquiries_count} запитвания\n"
            f"- {testimonials_count} отзива"
        )

        if not options["preserve_cities"]:
            result_message += _(
                f"\n- {cities_count} града\n"
                f"- {locations_count} локации\n"
                f"- {property_types_count} типа имоти\n"
                f"- {features_count} особености\n"
                f"- {team_members_count} членове на екипа"
            )
        else:
            result_message += _(
                "\n\nЗапазени са:\n"
                "- Градове и локации\n"
                "- Типове имоти\n"
                "- Особености\n"
                "- Членове на екипа"
            )

        self.stdout.write(self.style.SUCCESS(result_message))
