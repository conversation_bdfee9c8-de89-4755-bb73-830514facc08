"""
Форми за валидация и обработка на данни
"""

from django import forms
from django.core.exceptions import ValidationError
from django.conf import settings
from .models import PropertyImage
from .utils import validate_image_file


class PropertyImageForm(forms.ModelForm):
    """Форма за качване на снимки на имоти с разширена валидация"""

    class Meta:
        model = PropertyImage
        fields = ["image", "order", "alt_text"]
        widgets = {
            "alt_text": forms.TextInput(
                attrs={
                    "placeholder": "Алтернативен текст за изображението (по избор)",
                    "class": "form-control",
                }
            ),
            "order": forms.NumberInput(attrs={"min": 1, "class": "form-control"}),
        }

    def clean_image(self):
        """Разширена валидация на качения файл с изображение"""
        image = self.cleaned_data.get("image")

        if not image:
            return image

        # Използваме нашата utility функция за основна валидация
        is_valid, error_message = validate_image_file(image)
        if not is_valid:
            raise ValidationError(error_message)

        # Допълнителна проверка на MIME типа с python-magic (ако е наличен)
        try:
            import magic

            mime_type = magic.from_buffer(image.read(1024), mime=True)
            image.seek(0)  # Връщаме указателя в началото

            if mime_type not in settings.ALLOWED_IMAGE_TYPES:
                raise ValidationError(
                    f"Неподдържан MIME тип: {mime_type}. "
                    f"Разрешени са: {', '.join(settings.ALLOWED_IMAGE_TYPES)}"
                )
        except ImportError:
            # python-magic не е инсталиран, използваме само основната валидация
            pass
        except Exception as e:
            raise ValidationError(f"Грешка при проверка на файла: {str(e)}")

        return image

    def clean_order(self):
        """Валидация на поредността на снимката"""
        order = self.cleaned_data.get("order")
        property_obj = self.cleaned_data.get("property_obj")

        if order and property_obj:
            # Проверяваме дали вече съществува снимка с тази поредност
            existing = PropertyImage.objects.filter(
                property_obj=property_obj, order=order
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing.exists():
                raise ValidationError(
                    f"Вече съществува снимка с поредност {order} за този имот."
                )

        return order


class PropertyImageInlineFormSet(forms.BaseInlineFormSet):
    """Формсет за inline управление на снимки в админ панела"""

    def clean(self):
        """Валидация на целия формсет"""
        if any(self.errors):
            return

        orders = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get("DELETE", False):
                order = form.cleaned_data.get("order")
                if order:
                    if order in orders:
                        raise ValidationError(
                            "Не може да има две снимки с еднаква поредност."
                        )
                    orders.append(order)

        # Проверяваме дали има поне една снимка с поредност 1 (главна снимка)
        if orders and 1 not in orders:
            raise ValidationError(
                "Трябва да има поне една снимка с поредност 1 (главна снимка)."
            )


class BulkImageUploadForm(forms.Form):
    """Форма за масово качване на снимки"""

    # Simplified single file upload for now
    image = forms.ImageField(
        help_text="Изберете изображение за качване.",
    )

    def clean_image(self):
        """Валидация на файла"""
        image = self.cleaned_data.get("image")

        if image:
            is_valid, error_message = validate_image_file(image)
            if not is_valid:
                raise ValidationError(error_message)

        return image


def validate_image_dimensions(
    image, min_width=300, min_height=200, max_width=5000, max_height=5000
):
    """
    Валидира размерите на изображението

    Args:
        image: PIL Image обект или Django UploadedFile
        min_width: Минимална ширина в пиксели
        min_height: Минимална височина в пиксели
        max_width: Максимална ширина в пиксели
        max_height: Максимална височина в пиксели

    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        from PIL import Image

        if hasattr(image, "read"):
            img = Image.open(image)
        else:
            img = image

        width, height = img.size

        if width < min_width or height < min_height:
            return (
                False,
                f"Изображението е твърде малко. Минималните размери са {min_width}x{min_height} пиксела.",
            )

        if width > max_width or height > max_height:
            return (
                False,
                f"Изображението е твърде голямо. Максималните размери са {max_width}x{max_height} пиксела.",
            )

        return True, ""

    except Exception as e:
        return False, f"Грешка при проверка на размерите: {str(e)}"


def sanitize_filename(filename):
    """
    Почиства името на файла от потенциално опасни символи

    Args:
        filename: Оригиналното име на файла

    Returns:
        str: Почистеното име на файла
    """
    import re
    import os

    # Извличаме името и разширението
    name, ext = os.path.splitext(filename)

    # Премахваме всички символи освен букви, цифри, тире и долни черти
    name = re.sub(r"[^\w\-_\.]", "_", name)

    # Ограничаваме дължината на името
    if len(name) > 50:
        name = name[:50]

    # Връщаме почистеното име с разширението
    return f"{name}{ext.lower()}"
