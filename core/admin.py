from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from modeltranslation.admin import TabbedTranslationAdmin
from .models import (
    City,
    Location,
    PropertyType,
    Feature,
    TeamMember,
    Property,
    PropertyImage,
    Testimonial,
    ContactInquiry,
)


@admin.register(Location)
class LocationAdmin(TabbedTranslationAdmin):
    list_display = ("city", "name")
    list_filter = ("city",)
    search_fields = ("name",)
    ordering = ("city", "name")


@admin.register(City)
class CityAdmin(TabbedTranslationAdmin):
    list_display = ("name",)
    list_filter = ("name",)
    search_fields = ("name",)
    ordering = ("name",)


@admin.register(PropertyType)
class PropertyTypeAdmin(TabbedTranslationAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Feature)
class FeatureAdmin(TabbedTranslationAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(TeamMember)
class TeamMemberAdmin(TabbedTranslationAdmin):
    list_display = ("name", "title", "phone", "email")
    list_filter = ("title",)
    search_fields = ("name", "title", "email")


class PropertyImageInline(admin.TabularInline):
    """Inline админ за снимки на имоти с подобрено управление"""

    model = PropertyImage
    extra = 1
    ordering = ("order",)
    fields = ("image", "order", "alt_text", "image_preview", "image_info")
    readonly_fields = ("image_preview", "image_info")

    def image_preview(self, obj):
        """Показва миниатюра на изображението"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px; object-fit: cover;" />',
                obj.thumbnail.url if hasattr(obj, "thumbnail") else obj.image.url,
            )
        return "Няма изображение"

    image_preview.short_description = "Преглед"

    def image_info(self, obj):
        """Показва информация за изображението"""
        if obj.image:
            info_parts = []
            if obj.width and obj.height:
                info_parts.append(f"{obj.width}x{obj.height}px")
            if obj.file_size:
                info_parts.append(obj.file_size_formatted)
            return " | ".join(info_parts) if info_parts else "Няма данни"
        return "-"

    image_info.short_description = "Информация"


@admin.register(Property)
class PropertyAdmin(TabbedTranslationAdmin):
    list_display = (
        "title",
        "location",
        "property_type",
        "price",
        "area",
        "bedrooms",
        "is_published",
        "is_featured",
        "created_at",
    )
    list_filter = (
        "property_type",
        "location",
        "is_published",
        "is_featured",
        "assigned_broker",
        "created_at",
    )
    search_fields = ("title", "description", "location__name")
    filter_horizontal = ("features",)
    inlines = [PropertyImageInline]
    date_hierarchy = "created_at"
    list_editable = ("is_published", "is_featured")

    fieldsets = (
        (
            _("Основна информация"),
            {"fields": ("title", "property_type", "location")},
        ),
        (
            _("Детайли за имота"),
            {"fields": ("price", "area", "bedrooms", "description")},
        ),
        (_("Особености и брокер"), {"fields": ("features", "assigned_broker")}),
        (
            _("Настройки за публикуване"),
            {"fields": ("is_published", "is_featured"), "classes": ("collapse",)},
        ),
    )


@admin.register(Testimonial)
class TestimonialAdmin(TabbedTranslationAdmin):
    list_display = ("client_name", "is_active")
    list_filter = ("is_active",)
    search_fields = ("client_name", "quote")
    list_editable = ("is_active",)


@admin.register(ContactInquiry)
class ContactInquiryAdmin(admin.ModelAdmin):
    list_display = ("name", "email", "property", "created_at", "is_handled")
    list_filter = ("is_handled", "created_at", "property__property_type")
    search_fields = ("name", "email", "message", "property__title")
    readonly_fields = ("name", "email", "phone", "message", "property", "created_at")
    list_editable = ("is_handled",)
    date_hierarchy = "created_at"

    fieldsets = (
        (
            _("Информация за контакта"),
            {"fields": ("name", "email", "phone", "created_at")},
        ),
        (_("Запитване"), {"fields": ("property", "message")}),
        (_("Статус"), {"fields": ("is_handled",)}),
    )

    def has_add_permission(self, request):
        # Не позволяваме добавяне на запитвания чрез админ панела
        return False

    def has_delete_permission(self, request, obj=None):
        # Не позволяваме изтриване на запитвания
        return False
