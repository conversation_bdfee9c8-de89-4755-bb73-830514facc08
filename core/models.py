from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.core.files.base import ContentFile
from autoslug import AutoSlugField
from imagekit.models import ImageSpecField
from imagekit.processors import ResizeToFit, Thumbnail
from .utils import get_property_image_upload_path, optimize_image, validate_image_file


class City(models.Model):
    """Модел за градове"""

    name = models.CharField(max_length=100, verbose_name=_("Име на града"))

    class Meta:
        verbose_name = _("Град")
        verbose_name_plural = _("Градове")
        ordering = ["name"]

    def __str__(self):
        return self.name


class Location(models.Model):
    """Модел за локации (градове, квартали) с йерархична структура"""

    name = models.CharField(max_length=100, verbose_name=_("Име на локацията"))
    city = models.ForeignKey(
        City, on_delete=models.CASCADE, null=True, blank=True, verbose_name=_("Град")
    )

    class Meta:
        verbose_name = _("Локация")
        verbose_name_plural = _("Локации")
        ordering = ["city", "name"]

    def __str__(self):
        if self.city:
            return f"{self.name}, {self.city.name}"
        return self.name


class PropertyType(models.Model):
    """Модел за типовете имоти (Апартамент, Къща, и др.)"""

    name = models.CharField(max_length=50, verbose_name=_("Име на типа"))
    slug = AutoSlugField(
        populate_from="name",
        unique=True,
        always_update=False,
        max_length=50,
        verbose_name=_("URL адрес"),
    )

    class Meta:
        verbose_name = _("Тип имот")
        verbose_name_plural = _("Типове имоти")
        ordering = ["name"]

    def __str__(self):
        return self.name


class Feature(models.Model):
    """Модел за особеностите на имотите (Гараж, Асансьор, и др.)"""

    name = models.CharField(max_length=50, verbose_name=_("Име на особеността"))

    class Meta:
        verbose_name = _("Особеност")
        verbose_name_plural = _("Особености")
        ordering = ["name"]

    def __str__(self):
        return self.name


class TeamMember(models.Model):
    """Модел за членовете на екипа (брокери)"""

    name = models.CharField(max_length=100, verbose_name=_("Име"))
    photo = models.ImageField(
        upload_to="team_photos/", verbose_name=_("Снимка"), blank=True, null=True
    )
    phone = models.CharField(max_length=20, verbose_name=_("Телефонен номер"))
    email = models.EmailField(verbose_name=_("Имейл адрес"))
    title = models.CharField(max_length=100, verbose_name=_("Длъжност"))

    class Meta:
        verbose_name = _("Член на екипа")
        verbose_name_plural = _("Членове на екипа")
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.title}"


class Property(models.Model):
    """Основният модел за обявите за имоти"""

    title = models.CharField(max_length=200, verbose_name=_("Заглавие на обявата"))
    slug = AutoSlugField(
        populate_from="title",
        unique=True,
        always_update=False,
        max_length=200,
        verbose_name=_("URL адрес"),
    )
    property_type = models.ForeignKey(
        PropertyType, on_delete=models.CASCADE, verbose_name=_("Тип имот")
    )
    location = models.ForeignKey(
        Location, on_delete=models.CASCADE, verbose_name=_("Локация")
    )
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_("Цена"))
    area = models.IntegerField(verbose_name=_("Площ (кв.м.)"))
    bedrooms = models.PositiveIntegerField(verbose_name=_("Брой спални"), default=0)
    description = models.TextField(verbose_name=_("Пълно описание"))
    features = models.ManyToManyField(Feature, blank=True, verbose_name=_("Особености"))
    assigned_broker = models.ForeignKey(
        TeamMember, on_delete=models.CASCADE, verbose_name=_("Отговорен брокер")
    )
    is_published = models.BooleanField(
        default=True, verbose_name=_("Публикувана обява")
    )
    is_featured = models.BooleanField(default=False, verbose_name=_("Препоръчан имот"))
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_("Дата на създаване")
    )
    updated_at = models.DateTimeField(
        auto_now=True, verbose_name=_("Дата на обновяване")
    )

    class Meta:
        verbose_name = _("Имот")
        verbose_name_plural = _("Имоти")
        ordering = ["-created_at"]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse("core:property-detail", kwargs={"slug": self.slug})

    @property
    def main_image(self):
        """Връща главната снимка на имота (първата в поредността)"""
        return self.images.filter(order=1).first()

    @property
    def price_formatted(self):
        """Форматирана цена с валута"""
        return f"{self.price:,.0f} лв."


class PropertyImage(models.Model):
    """Модел за галерията със снимки към всеки имот с оптимизация"""

    property_obj = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name="images",
        verbose_name=_("Имот"),
    )
    image = models.ImageField(
        upload_to=get_property_image_upload_path, verbose_name=_("Снимка")
    )
    order = models.PositiveIntegerField(default=1, verbose_name=_("Поредност"))

    # Метаданни за изображението
    alt_text = models.CharField(
        max_length=255, blank=True, verbose_name=_("Алтернативен текст")
    )
    width = models.PositiveIntegerField(null=True, blank=True, verbose_name=_("Ширина"))
    height = models.PositiveIntegerField(
        null=True, blank=True, verbose_name=_("Височина")
    )
    file_size = models.PositiveIntegerField(
        null=True, blank=True, verbose_name=_("Размер на файла")
    )

    # Автоматично генерирани варианти на изображението
    thumbnail = ImageSpecField(
        source="image",
        processors=[Thumbnail(300, 200)],
        format="JPEG",
        options={"quality": 80},
    )

    medium = ImageSpecField(
        source="image",
        processors=[ResizeToFit(800, 600)],
        format="JPEG",
        options={"quality": 85},
    )

    large = ImageSpecField(
        source="image",
        processors=[ResizeToFit(1920, 1080)],
        format="JPEG",
        options={"quality": 90},
    )

    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_("Дата на създаване")
    )
    updated_at = models.DateTimeField(
        auto_now=True, verbose_name=_("Дата на обновяване")
    )

    class Meta:
        verbose_name = _("Снимка на имот")
        verbose_name_plural = _("Снимки на имоти")
        ordering = ["order"]
        unique_together = ["property_obj", "order"]

    def __str__(self):
        return f"Снимка {self.order} - {self.property_obj.title}"

    def clean(self):
        """Валидация на качения файл"""
        if self.image:
            is_valid, error_message = validate_image_file(self.image)
            if not is_valid:
                from django.core.exceptions import ValidationError

                raise ValidationError({"image": error_message})

    def save(self, *args, **kwargs):
        """Преработваме save метода за оптимизация на изображението"""
        # Проверяваме дали това е нов файл или съществуващ
        is_new_image = self.pk is None or (
            self.pk and hasattr(self.image, "content_type")
        )

        if self.image and is_new_image:
            # Оптимизираме изображението преди запазване - само за нови файлове
            optimized_image = optimize_image(
                self.image, max_width=1920, max_height=1080
            )

            # Запазваме оптимизираното изображение
            self.image.save(self.image.name, optimized_image, save=False)

        # Извличаме/обновяваме метаданни
        if self.image:
            from PIL import Image

            img = Image.open(self.image)
            self.width = img.width
            self.height = img.height
            self.file_size = self.image.size

            # Генерираме alt текст ако не е зададен
            if not self.alt_text:
                self.alt_text = f"Снимка {self.order} от {self.property_obj.title}"

        super().save(*args, **kwargs)

    @property
    def aspect_ratio(self):
        """Връща съотношението ширина/височина"""
        if self.width and self.height:
            return round(self.width / self.height, 2)
        return None

    @property
    def file_size_formatted(self):
        """Връща форматирания размер на файла"""
        if self.file_size:
            if self.file_size < 1024:
                return f"{self.file_size} B"
            elif self.file_size < 1024 * 1024:
                return f"{round(self.file_size / 1024, 1)} KB"
            else:
                return f"{round(self.file_size / (1024 * 1024), 1)} MB"
        return "Неизвестен"


class Testimonial(models.Model):
    """Модел за отзиви от клиенти"""

    client_name = models.CharField(max_length=100, verbose_name=_("Име на клиента"))
    quote = models.TextField(verbose_name=_("Текст на отзива"))
    is_active = models.BooleanField(default=True, verbose_name=_("Активен отзив"))

    class Meta:
        verbose_name = _("Отзив")
        verbose_name_plural = _("Отзиви")
        ordering = ["client_name"]

    def __str__(self):
        return f"Отзив от {self.client_name}"


class ContactInquiry(models.Model):
    """Модел за съхранение на запитвания от формите за контакт"""

    name = models.CharField(max_length=100, verbose_name=_("Име"))
    email = models.EmailField(verbose_name=_("Имейл адрес"))
    phone = models.CharField(max_length=20, verbose_name=_("Телефонен номер"))
    message = models.TextField(verbose_name=_("Съобщение"))
    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Имот (ако е за конкретна обява)"),
    )
    created_at = models.DateTimeField(
        auto_now_add=True, verbose_name=_("Дата на получаване")
    )
    is_handled = models.BooleanField(
        default=False, verbose_name=_("Обработено запитване")
    )

    class Meta:
        verbose_name = _("Запитване за контакт")
        verbose_name_plural = _("Запитвания за контакт")
        ordering = ["-created_at"]

    def __str__(self):
        if self.property:
            return f"Запитване от {self.name} за {self.property.title}"
        return f"Общо запитване от {self.name}"
