### Фаза 1: Основи на проекта и Модели на данни (Django)

-   [x] **Модели**: Дефиниране на `Location` модел (`core/models.py`)
-   [x] **Модели**: Дефи<PERSON><PERSON><PERSON>а<PERSON>е на `PropertyType` модел (`core/models.py`)
-   [x] **Модели**: Дефиниране на `Feature` модел (`core/models.py`)
-   [x] **Модели**: Дефиниране на `TeamMember` модел (`core/models.py`)
-   [x] **Модели**: Дефиниране на `Property` модел (основен, с всички релации) (`core/models.py`)
-   [x] **Модели**: Дефиниране на `PropertyImage` модел (за галерия) (`core/models.py`)
-   [x] **Модели**: Дефинира<PERSON>е на `Testimonial` модел (за отзиви) (`core/models.py`)
-   [x] **Модели**: Дефиниране на `ContactInquiry` модел (за запитвания) (`core/models.py`)
-   [x] **Миграции**: Създаване и прилагане на първоначалните миграции за базата данни (`manage.py makemigrations` & `migrate`)

### Фаза 2: Административен панел (Django Admin)

-   [x] **Админ**: Регистриране на всички модели в `core/admin.py`.
-   [x] **Админ**: Конфигуриране на `PropertyAdmin` с `PropertyImage` като `inlines`.
-   [x] **Админ**: Добавяне на `list_display`, `list_filter`, `search_fields` за `PropertyAdmin`.
-   [x] **Админ**: Настройване на `prepopulated_fields` за `slug` в `PropertyAdmin`.
-   [x] **Админ**: Конфигуриране на `list_display` и `list_filter` за останалите модели (`TeamMember`, `ContactInquiry` и др.).
-   [x] **Админ**: Настройване на `readonly_fields` за `ContactInquiryAdmin`.

### Фаза 3: Бизнес логика (Views & URLs)

-   [x] **URL-и и Изгледи**: Имплементиране на `home_view` (`/`), който показва препоръчани имоти и отзиви.
-   [x] **URL-и и Изгледи**: Имплементиране на `property_list_view` (`/properties/`) с логика за филтриране.
-   [x] **URL-и и Изгледи**: Имплементиране на `property_detail_view` (`/properties/<slug>/`).
-   [x] **URL-и и Изгледи**: Имплементиране на `contact_inquiry_create_view` (за обработка на POST заявки от формите).
-   [x] **URL-и и Изгледи**: Имплементиране на статичните страници (`About`, `Contact`, `Privacy Policy`, `Terms of Service`) чрез `TemplateView`.

### Фаза 4: Frontend Шаблони (Django Templates & Partials)

-   [x] **Шаблони**: Създаване на `base.html` с `head`, `body` и блокове за съдържание (`{% block %}`).
-   [x] **Partials**: Създаване на `_header.html` (вкл. навигация и превключвател за тема).
-   [x] **Partials**: Създаване на `_footer.html`.
-   [x] **Partials**: Създаване на `_property_card.html` (карта на имот за списъци).
-   [x] **Partials**: Създаване на `property_list_results.html` (за htmx резултатите).
-   [x] **Шаблони**: Имплементиране на `index.html` (Начална страница).
-   [x] **Шаблони**: Имплементиране на `property_list.html` (Списък с обяви).
-   [x] **Шаблони**: Имплементиране на `property_detail.html` (Детайлна страница).
-   [x] **Шаблони**: Имплементиране на `about.html`, `contact.html` и другите статични страници.

### Фаза 5: Frontend Assets и Интерактивност (CSS/JS)

-   [x] **CSS**: Финализиране на Tailwind CSS теми (цветове, шрифтове, радиус) в `static/css/main.css`.
-   [x] **JS**: Имплементиране на Alpine.js компонент за мобилното меню (хамбургер).
-   [x] **JS**: Имплементиране на Alpine.js компонент за превключвателя на тъмна/светла тема.
-   [x] **JS**: Имплементиране на Alpine.js за карусела с отзиви на началната страница.
-   [x] **htmx**: Интегриране на htmx за динамично филтриране в `property_list.html`.
-   [x] **htmx**: Интегриране на htmx за изпращане на формите за контакт без презареждане.
-   [x] **CSS**: Прилагане на responsive стилове (mobile-first) за всички страници и компоненти.

### Фаза 6: Съдържание и Финализиране

-   [x] **Съдържание**: Въвеждане на примерни данни през админ панела (имоти, брокери, локации, отзиви).
-   [x] **Тестване**: Цялостен преглед и тестване на функционалностите (филтри, форми, навигация).
-   [x] **Оптимизация**: Проверка на скоростта на зареждане и оптимизация на изображения.
-   [x] **Функционалност**: Simple search bar в момента работи.

---

### Фаза 7: Интернационализация (i18n)

-   [x] **Настройки**: Конфигуриране на `settings.py` (LANGUAGES, LocaleMiddleware).
-   [x] **URL-и**: Обвиване на основните URL пътища с `i18n_patterns` в `config/urls.py`.
-   [x] **Библиотека**: Инсталиране и конфигуриране на `django-modeltranslation`.
-   [x] **Модели**: Създаване на `core/translation.py` и регистриране на моделите за превод (`Property`, `Feature` и др.).
-   [x] **Миграции**: Създаване и прилагане на миграции за преводимите полета.
-   [x] **Админ**: Проверка и тестване на многоезичния интерфейс в админ панела.
-   [x] **Админ**: Имлементиране на `TabbedTranslationAdmin` за по-добър потребителски интерфейс
-   [x] **Шаблони**: Обвиване на всички статични текстове в шаблоните с `{% trans %}`.
-   [x] **Python код**: Използване на `gettext_lazy` за текстове в Python файлове (формуляри, модели).
-   [x] **Преводи**: Създаване на `.po` файлове за `en`, `el`, `ru` (`makemessages`).
-   [x] **Преводи**: Попълване на преводите в `.po` файловете.
-   [x] **UI**: Имплементиране на компонент за смяна на езика в хедъра.
-   [ ] **Локализация**: Конфигуриране на `formats.py` за различните езици.
-   [ ] **SEO**: Имплементиране на `hreflang` тагове за алтернативните езикови версии.
-   [x] **Тестване**: Цялостно тестване на многоезичната функционалност.

---

## 🎯 Следващи приоритети:

-   [x] **СПЕШНО**: Създаване на тестове за всяка една функционалност
-   [x] **Функционалност**: Оптимизация на изображенията при качване.
-   [x] **Бъг**: Качените снимки не се зареждат в темплейтите.
-   [ ] **Функционалност**: На мобилен формат, менюто не се включва.
-   [ ] **Функционалност**: Снимките не се изтриват при тяхното изтриване в админ панела. Т.е. ако администратора влезне да обработва имота и изтрие някоя от снимките, то тази изтрита снимка остава в директорията media/property_images и не се изтрива
-   [ ] **Функционалност**: Снимките не се изтриват при изтриване на имота от админ панела. Т.е ако администратор изтрие имота, то снимките не се премахват от media/property_images
-   [ ] **Функционалност**: Да се инсталира пакета django-admin-sortable2 и да се използва за подредба на снимките в имотите

---

### Фаза 8: Подобрение на URL адресите (Slugs) ✅

-   [x] **Библиотеки**: Инсталиране на необходимите пакети:
    -   `django-autoslug` - за автоматично генериране на slug-ове ✅
    -   `python-slugify` - за подобра Unicode поддръжка (кирилица, гръцки) ✅
-   [x] **Настройки**: Конфигуриране на `settings.py`:
    -   Добавяне на `AUTOSLUG_MODELTRANSLATION_ENABLE = True` ✅
    -   Използване на default slugify функция (Unicode поддръжката ще се подобри в бъдеще) ✅
-   [x] **Модели**: Замяна на `models.SlugField` с `AutoSlugField` в моделите:
    -   `Property` модел - основен приоритет ✅
    -   `PropertyType` модел - за консистентност ✅
-   [x] **Конфигурация**: Настройване на `AutoSlugField` за автоматично генериране:
    -   `populate_from='title'` (само от заглавието) ✅
    -   `unique=True` за уникалност ✅
    -   `always_update=False` за запазване на URL-и при редакция ✅
    -   Премахване на manual slug generation логика ✅
-   [x] **Преводи**: Добавяне на `slug` към преводимите полета в `core/translation.py`:
    -   В `PropertyTranslationOptions` - добавяне на `'slug'` към `fields` ✅
    -   В `PropertyTypeTranslationOptions` - добавяне на `'slug'` към `fields` ✅
-   [x] **Админ**: Премахване на slug полетата от админ интерфейса:
    -   Изтриване на `prepopulated_fields` за slug в `PropertyAdmin` ✅
    -   Изтриване на slug от `list_display` ако е добавен ✅
    -   Премахване на slug от fieldsets ✅
-   [x] **Миграции**: Създаване на схемна миграция за промените в моделите ✅
-   [x] **Тестване**: Създаване на тестове за автоматичното генериране:
    -   Тест за генериране на slug от английски заглавия ✅
    -   Тест за генериране на slug от български заглавия ✅
    -   Тест за уникалност на slug-овете ✅
    -   Тест за автоматично обновяване при промяна на заглавие ✅
    -   Тест за URL безопасност ✅
    -   Тест за интеграция с modeltranslation ✅
-   [x] **Валидация**: Проверка на генерираните slug-ове:
    -   URL-безопасност на всички символи ✅
    -   Автоматично генериране работи ✅
    -   Уникалност работи ✅
    -   Обработка на специални символи ✅

**🎯 Резултат:** AutoSlugField е успешно имплементиран и работи отлично за автоматично генериране на URL-безопасни, уникални slug-ове. Администраторите вече не са длъжни да пишат slug-ове ръчно - те се генерират автоматично от заглавието.

**✅ Подобрение:** Unicode поддръжката (кирилица) е значително подобрена чрез конфигуриране на `python-slugify` библиотеката. Сега кирилските заглавия се транслитерират правилно:

-   "Луксозен апартамент в центъра на София" → "luksozen-apartament-v-tsentra-na-sofiia"
-   "Къща с двор в Борово" → "kshcha-s-dvor-v-borovo"
-   "Офис в бизнес център Сердика" → "ofis-v-biznes-tsentr-serdika"
